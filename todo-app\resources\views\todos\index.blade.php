<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Todo App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>
    <meta name="csrf-token" content="{{ csrf_token() }}">
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-2xl">
        <h1 class="text-3xl font-bold text-gray-800 mb-8 text-center">Todo App</h1>
        
        @if(session('success'))
            <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                {{ session('success') }}
            </div>
        @endif

        <!-- Add Todo Form -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6" x-data="{ showForm: false }">
            <button 
                @click="showForm = !showForm" 
                class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded transition duration-200"
            >
                <span x-text="showForm ? 'Cancel' : 'Add New Todo'"></span>
            </button>
            
            <form 
                x-show="showForm" 
                x-transition
                action="{{ route('todos.store') }}" 
                method="POST" 
                class="mt-4"
            >
                @csrf
                <div class="mb-4">
                    <label for="title" class="block text-gray-700 text-sm font-bold mb-2">Title</label>
                    <input 
                        type="text" 
                        id="title" 
                        name="title" 
                        required
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter todo title..."
                    >
                    @error('title')
                        <p class="text-red-500 text-xs mt-1">{{ $message }}</p>
                    @enderror
                </div>
                
                <div class="mb-4">
                    <label for="description" class="block text-gray-700 text-sm font-bold mb-2">Description (Optional)</label>
                    <textarea 
                        id="description" 
                        name="description" 
                        rows="3"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter todo description..."
                    ></textarea>
                </div>
                
                <button 
                    type="submit" 
                    class="bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded transition duration-200"
                >
                    Add Todo
                </button>
            </form>
        </div>

        <!-- Todo List -->
        <div class="space-y-4">
            @forelse($todos as $todo)
                <div class="bg-white rounded-lg shadow-md p-4 flex items-center justify-between">
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold {{ $todo->completed ? 'line-through text-gray-500' : 'text-gray-800' }}">
                            {{ $todo->title }}
                        </h3>
                        @if($todo->description)
                            <p class="text-gray-600 mt-1 {{ $todo->completed ? 'line-through' : '' }}">
                                {{ $todo->description }}
                            </p>
                        @endif
                        <p class="text-xs text-gray-400 mt-2">
                            Created: {{ $todo->created_at->format('M d, Y H:i') }}
                        </p>
                    </div>
                    
                    <div class="flex items-center space-x-2 ml-4">
                        <!-- Toggle Complete -->
                        <form action="{{ route('todos.update', $todo) }}" method="POST" class="inline">
                            @csrf
                            @method('PATCH')
                            <button 
                                type="submit" 
                                class="px-3 py-1 rounded text-sm font-medium transition duration-200 {{ $todo->completed ? 'bg-yellow-500 hover:bg-yellow-600 text-white' : 'bg-green-500 hover:bg-green-600 text-white' }}"
                            >
                                {{ $todo->completed ? 'Undo' : 'Complete' }}
                            </button>
                        </form>
                        
                        <!-- Delete -->
                        <form 
                            action="{{ route('todos.destroy', $todo) }}" 
                            method="POST" 
                            class="inline"
                            x-data="{ confirmDelete: false }"
                        >
                            @csrf
                            @method('DELETE')
                            <button 
                                type="button"
                                @click="confirmDelete = true"
                                class="px-3 py-1 bg-red-500 hover:bg-red-600 text-white rounded text-sm font-medium transition duration-200"
                            >
                                Delete
                            </button>
                            
                            <!-- Confirmation Modal -->
                            <div 
                                x-show="confirmDelete" 
                                x-transition
                                class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
                                @click.away="confirmDelete = false"
                            >
                                <div class="bg-white p-6 rounded-lg shadow-lg max-w-sm w-full mx-4">
                                    <h3 class="text-lg font-semibold mb-4">Confirm Delete</h3>
                                    <p class="text-gray-600 mb-6">Are you sure you want to delete this todo?</p>
                                    <div class="flex space-x-3">
                                        <button 
                                            type="submit"
                                            class="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded transition duration-200"
                                        >
                                            Delete
                                        </button>
                                        <button 
                                            type="button"
                                            @click="confirmDelete = false"
                                            class="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-800 py-2 px-4 rounded transition duration-200"
                                        >
                                            Cancel
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            @empty
                <div class="bg-white rounded-lg shadow-md p-8 text-center">
                    <div class="text-gray-400 mb-4">
                        <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                        </svg>
                    </div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No todos yet</h3>
                    <p class="text-gray-500">Get started by adding your first todo!</p>
                </div>
            @endforelse
        </div>
        
        @if($todos->count() > 0)
            <div class="mt-8 text-center">
                <div class="inline-flex items-center space-x-4 text-sm text-gray-600">
                    <span>Total: {{ $todos->count() }}</span>
                    <span>Completed: {{ $todos->where('completed', true)->count() }}</span>
                    <span>Pending: {{ $todos->where('completed', false)->count() }}</span>
                </div>
            </div>
        @endif
    </div>
</body>
</html>
