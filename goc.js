#!/usr/bin/env node

// GOC Agent - Intelligent Coding Assistant
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Check if built version exists
const cliPath = path.join(__dirname, 'dist', 'cli.js');
if (!fs.existsSync(cliPath)) {
  console.error('❌ GOC Agent not built. Please run: npm run build');
  process.exit(1);
}

const args = process.argv.slice(2);

// If no arguments provided, show a friendly welcome
if (args.length === 0) {
  console.log(`
🤖 GOC Agent - Intelligent Coding Assistant

Core Commands:
  goc chat                    # Interactive chat with your codebase
  goc agent                   # Autonomous coding agent
  goc config                  # Setup AI providers

Options:
  goc chat -p ollama          # Use specific provider
  goc agent --auto            # Fully autonomous mode
  goc --help                  # Show all options

Quick Start:
  1. Setup: goc config
  2. Chat:  goc chat
  3. Code:  goc agent

🚀 Ready to code with AI!
`);
  process.exit(0);
}

// Run the CLI
const child = spawn('node', [cliPath, ...args], {
  stdio: 'inherit',
  cwd: process.cwd()
});

child.on('exit', (code) => {
  process.exit(code);
});

child.on('error', (error) => {
  console.error('❌ Failed to start GOC Agent:', error.message);
  process.exit(1);
});
