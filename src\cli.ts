#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import { ConfigManager } from './config/ConfigManager';
import { CodebaseAnalyzer } from './codebase/CodebaseAnalyzer';
import { AIProviderManager } from './ai/AIProviderManager';
import { CommandHandler } from './commands/CommandHandler';
import { Logger } from './utils/Logger';

const program = new Command();

async function main() {
  try {
    // Initialize core components
    const config = new ConfigManager();
    const logger = new Logger();
    const codebaseAnalyzer = new CodebaseAnalyzer(config, logger);
    const aiManager = new AIProviderManager(config, logger);
    const commandHandler = new CommandHandler(config, codebaseAnalyzer, aiManager, logger);

    // Setup CLI
    program
      .name('goc')
      .description('GOC Agent - A coding agent with multiple AI provider support')
      .version('1.0.0');

    // Main chat command - the primary interface
    program
      .command('chat')
      .description('Start interactive chat session')
      .option('-p, --provider <provider>', 'AI provider (ollama, openai, groq, gemini)')
      .option('-m, --model <model>', 'Model to use')
      .action(async (options) => {
        await commandHandler.handleChat(options);
      });

    // Agent mode - autonomous coding
    program
      .command('agent')
      .description('Start autonomous agent mode')
      .option('-p, --provider <provider>', 'AI provider to use')
      .option('-m, --model <model>', 'Model to use')
      .option('-t, --task <task>', 'Task description')
      .option('--auto', 'Enable fully autonomous mode')
      .action(async (options) => {
        await commandHandler.handleAgent(options);
      });

    // Configuration - simplified
    program
      .command('config')
      .description('Configure providers and settings')
      .action(async () => {
        await commandHandler.handleConfig();
      });

    // Parse arguments
    await program.parseAsync(process.argv);

  } catch (error) {
    console.error(chalk.red('Error:'), error instanceof Error ? error.message : String(error));
    process.exit(1);
  }
}

// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error(chalk.red('Unhandled Rejection at:'), promise, chalk.red('reason:'), reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error(chalk.red('Uncaught Exception:'), error);
  process.exit(1);
});

if (require.main === module) {
  main();
}

export { main };
