import { ConfigManager } from '../config/ConfigManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { AIProviderManager } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
export declare class CommandHandler {
    private config;
    private codebaseAnalyzer;
    private aiManager;
    private logger;
    private contextBuilder;
    private fileEditor;
    private agentCore;
    private memoryManager;
    private trainingManager;
    constructor(config: ConfigManager, codebaseAnalyzer: CodebaseAnalyzer, aiManager: AIProviderManager, logger: Logger);
    handleConfig(): Promise<void>;
    handleChat(options: any): Promise<void>;
    handleAnalyze(options: any): Promise<void>;
    handleEdit(file: string, options: any): Promise<void>;
    handleExplain(file: string, options: any): Promise<void>;
    handleSearch(query: string, options: any): Promise<void>;
    handleAgent(options: any): Promise<void>;
    private showAgentMenu;
    private handleAgentMode;
    private handleAutoMode;
    private executeAgentTask;
    private handleAnalyzeMode;
    private handleEditMode;
    private handleCreateMode;
    private showCurrentConfig;
    private configureProviders;
    private setDefaultProvider;
    private configureCodebase;
    private showChatHelp;
    private promptForTask;
    private promptForGoal;
    private formatBytes;
}
//# sourceMappingURL=CommandHandler.d.ts.map