#!/usr/bin/env node
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.main = main;
const commander_1 = require("commander");
const chalk_1 = __importDefault(require("chalk"));
const ConfigManager_1 = require("./config/ConfigManager");
const CodebaseAnalyzer_1 = require("./codebase/CodebaseAnalyzer");
const AIProviderManager_1 = require("./ai/AIProviderManager");
const CommandHandler_1 = require("./commands/CommandHandler");
const Logger_1 = require("./utils/Logger");
const program = new commander_1.Command();
async function main() {
    try {
        // Initialize core components
        const config = new ConfigManager_1.ConfigManager();
        const logger = new Logger_1.Logger();
        const codebaseAnalyzer = new CodebaseAnalyzer_1.CodebaseAnalyzer(config, logger);
        const aiManager = new AIProviderManager_1.AIProviderManager(config, logger);
        const commandHandler = new CommandHandler_1.CommandHandler(config, codebaseAnalyzer, aiManager, logger);
        // Setup CLI
        program
            .name('goc')
            .description('GOC Agent - A coding agent with multiple AI provider support')
            .version('1.0.0');
        // Main chat command - the primary interface
        program
            .command('chat')
            .description('Start interactive chat session')
            .option('-p, --provider <provider>', 'AI provider (ollama, openai, groq, gemini)')
            .option('-m, --model <model>', 'Model to use')
            .action(async (options) => {
            await commandHandler.handleChat(options);
        });
        // Agent mode - autonomous coding
        program
            .command('agent')
            .description('Start autonomous agent mode')
            .option('-p, --provider <provider>', 'AI provider to use')
            .option('-m, --model <model>', 'Model to use')
            .option('-t, --task <task>', 'Task description')
            .option('--auto', 'Enable fully autonomous mode')
            .action(async (options) => {
            await commandHandler.handleAgent(options);
        });
        // Configuration - simplified
        program
            .command('config')
            .description('Configure providers and settings')
            .action(async () => {
            await commandHandler.handleConfig();
        });
        // Parse arguments
        await program.parseAsync(process.argv);
    }
    catch (error) {
        console.error(chalk_1.default.red('Error:'), error instanceof Error ? error.message : String(error));
        process.exit(1);
    }
}
// Handle unhandled rejections
process.on('unhandledRejection', (reason, promise) => {
    console.error(chalk_1.default.red('Unhandled Rejection at:'), promise, chalk_1.default.red('reason:'), reason);
    process.exit(1);
});
// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
    console.error(chalk_1.default.red('Uncaught Exception:'), error);
    process.exit(1);
});
if (require.main === module) {
    main();
}
//# sourceMappingURL=cli.js.map