import inquirer from 'inquirer';
import chalk from 'chalk';
import boxen from 'boxen';
import Table from 'cli-table3';
import { ConfigManager } from '../config/ConfigManager';
import { CodebaseAnalyzer } from '../codebase/CodebaseAnalyzer';
import { AIProviderManager, ChatMessage } from '../ai/AIProviderManager';
import { Logger } from '../utils/Logger';
import { ContextBuilder } from './ContextBuilder';
import { FileEditor } from './FileEditor';
import { AgentCore } from '../agent/AgentCore';
import { MemoryManager } from '../agent/MemoryManager';
import { TrainingManager } from '../training/TrainingManager';

export class CommandHandler {
  private config: ConfigManager;
  private codebaseAnalyzer: CodebaseAnalyzer;
  private aiManager: AIProviderManager;
  private logger: Logger;
  private contextBuilder: ContextBuilder;
  private fileEditor: FileEditor;
  private agentCore: AgentCore;
  private memoryManager: MemoryManager;
  private trainingManager: TrainingManager;

  constructor(
    config: ConfigManager,
    codebaseAnalyzer: CodebaseAnalyzer,
    aiManager: AIProviderManager,
    logger: Logger
  ) {
    this.config = config;
    this.codebaseAnalyzer = codebaseAnalyzer;
    this.aiManager = aiManager;
    this.logger = logger;
    this.contextBuilder = new ContextBuilder(codebaseAnalyzer, logger);
    this.fileEditor = new FileEditor(logger);
    this.memoryManager = new MemoryManager(logger);
    this.trainingManager = new TrainingManager(logger, this.memoryManager, codebaseAnalyzer);
    this.agentCore = new AgentCore(aiManager, codebaseAnalyzer, this.contextBuilder, this.fileEditor, logger);
  }

  public async handleConfig(): Promise<void> {
    const choices = [
      'View current configuration',
      'Configure AI providers',
      'Set default provider',
      'Configure codebase settings',
      'Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: 'What would you like to configure?',
        choices
      }
    ]);

    switch (action) {
      case 'View current configuration':
        await this.showCurrentConfig();
        break;
      case 'Configure AI providers':
        await this.configureProviders();
        break;
      case 'Set default provider':
        await this.setDefaultProvider();
        break;
      case 'Configure codebase settings':
        await this.configureCodebase();
        break;
    }
  }

  public async handleChat(options: any): Promise<void> {
    const providerName = options.provider;
    const model = options.model;

    // Check if provider is available
    if (providerName) {
      const provider = this.aiManager.getProvider(providerName);
      if (!provider) {
        this.logger.error(`Provider '${providerName}' not found or not configured`);
        return;
      }
    }

    this.logger.info('Starting interactive chat session...');
    this.logger.info('Type "exit" to quit, "help" for commands');

    const messages: ChatMessage[] = [];
    
    // Add system context
    const context = await this.contextBuilder.buildContext();
    if (context) {
      messages.push({
        role: 'system',
        content: context
      });
    }

    while (true) {
      const { input } = await inquirer.prompt([
        {
          type: 'input',
          name: 'input',
          message: chalk.green('You:'),
        }
      ]);

      if (input.toLowerCase() === 'exit') {
        break;
      }

      if (input.toLowerCase() === 'help') {
        this.showChatHelp();
        continue;
      }

      if (input.trim() === '') {
        continue;
      }

      messages.push({
        role: 'user',
        content: input
      });

      try {
        this.logger.loading('Thinking...');
        const response = await this.aiManager.chat(messages, providerName, model);
        
        console.log(chalk.blue('Assistant:'), response.content);
        
        if (response.usage) {
          this.logger.debug(`Tokens: ${response.usage.totalTokens} (${response.usage.promptTokens} + ${response.usage.completionTokens})`);
        }

        messages.push({
          role: 'assistant',
          content: response.content
        });

      } catch (error) {
        this.logger.error(`Chat error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    this.logger.info('Chat session ended');
  }

  public async handleAnalyze(options: any): Promise<void> {
    this.logger.loading('Analyzing codebase...');
    
    try {
      const context = await this.codebaseAnalyzer.scanCodebase();
      
      console.log(boxen(
        `Codebase Analysis\n\n` +
        `Files: ${context.totalFiles}\n` +
        `Total Size: ${this.formatBytes(context.totalSize)}\n` +
        `Languages: ${context.languages.join(', ')}\n`,
        { padding: 1, borderColor: 'blue' }
      ));

      if (options.files) {
        const files = await this.codebaseAnalyzer.searchFiles(options.files);
        console.log(`\nMatching files (${files.length}):`);
        files.forEach(file => {
          console.log(`  ${file.relativePath}`);
        });
      } else {
        console.log('\nProject Structure:');
        console.log(context.structure);
      }

    } catch (error) {
      this.logger.error(`Analysis failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleEdit(file: string, options: any): Promise<void> {
    if (!options.instruction) {
      const { instruction } = await inquirer.prompt([
        {
          type: 'input',
          name: 'instruction',
          message: 'What changes would you like to make?',
          validate: (input) => input.trim() !== '' || 'Please provide an instruction'
        }
      ]);
      options.instruction = instruction;
    }

    try {
      await this.fileEditor.editFile(file, options.instruction, this.aiManager, this.contextBuilder);
      this.logger.success(`File ${file} edited successfully`);
    } catch (error) {
      this.logger.error(`Edit failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleExplain(file: string, options: any): Promise<void> {
    try {
      const content = await this.codebaseAnalyzer.getFileContent(file);
      const context = await this.contextBuilder.buildFileContext(file);
      
      let prompt = `Please explain this code:\n\n${content}`;
      
      if (options.lines) {
        const [start, end] = options.lines.split('-').map(Number);
        const lines = content.split('\n');
        const selectedLines = lines.slice(start - 1, end).join('\n');
        prompt = `Please explain these lines (${start}-${end}) from ${file}:\n\n${selectedLines}`;
      }

      const messages: ChatMessage[] = [
        { role: 'system', content: context },
        { role: 'user', content: prompt }
      ];

      this.logger.loading('Analyzing code...');
      const response = await this.aiManager.chat(messages);
      
      console.log(boxen(response.content, { 
        padding: 1, 
        borderColor: 'green',
        title: `Code Explanation: ${file}`
      }));

    } catch (error) {
      this.logger.error(`Explanation failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  public async handleSearch(query: string, options: any): Promise<void> {
    try {
      const searchType = options.type || 'text';
      const results = await this.codebaseAnalyzer.searchFiles(query, searchType);
      
      if (results.length === 0) {
        this.logger.info('No matches found');
        return;
      }

      console.log(`\nFound ${results.length} matches:`);
      results.forEach(file => {
        console.log(`  ${chalk.blue(file.relativePath)} (${file.size} bytes)`);
      });

    } catch (error) {
      this.logger.error(`Search failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }



  public async handleAgent(options: any): Promise<void> {
    // If auto mode is enabled, go straight to autonomous mode
    if (options.auto) {
      return await this.handleAutoMode(options);
    }

    // If task is provided, execute it directly
    if (options.task) {
      return await this.executeAgentTask(options.task, options);
    }

    // Otherwise, show agent menu
    await this.showAgentMenu(options);
  }

  private async showAgentMenu(options: any): Promise<void> {
    const choices = [
      '💬 Chat about codebase',
      '🔍 Analyze and explain code',
      '✏️  Edit/improve code',
      '🏗️  Create new features',
      '🤖 Autonomous mode (with confirmation)',
      '🚀 Full auto mode (autonomous)',
      '⚙️  Configure settings',
      '❌ Exit'
    ];

    const { action } = await inquirer.prompt([
      {
        type: 'list',
        name: 'action',
        message: '🤖 What would you like me to help you with?',
        choices
      }
    ]);

    switch (action) {
      case '💬 Chat about codebase':
        await this.handleChat(options);
        break;
      case '🔍 Analyze and explain code':
        await this.handleAnalyzeMode(options);
        break;
      case '✏️  Edit/improve code':
        await this.handleEditMode(options);
        break;
      case '🏗️  Create new features':
        await this.handleCreateMode(options);
        break;
      case '🤖 Autonomous mode (with confirmation)':
        await this.handleAgentMode(options);
        break;
      case '🚀 Full auto mode (autonomous)':
        await this.handleAutoMode(options);
        break;
      case '⚙️  Configure settings':
        await this.handleConfig();
        break;
      case '❌ Exit':
        this.logger.info('👋 Goodbye!');
        break;
    }
  }

  private async handleAgentMode(options: any): Promise<void> {
    const task = await this.promptForTask();
    await this.executeAgentTask(task, options);
  }

  private async handleAutoMode(options: any): Promise<void> {
    const goal = options.goal || await this.promptForGoal();

    this.logger.info(`🚀 Starting Auto Agent Mode`);
    this.logger.info(`🎯 Goal: ${goal}`);

    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: chalk.yellow('⚠️  Auto mode will make autonomous changes to your codebase. Continue?'),
        default: false
      }
    ]);

    if (!confirm) {
      this.logger.info('Auto agent mode cancelled');
      return;
    }

    try {
      await this.agentCore.startAutoAgent(goal, options);
    } catch (error) {
      this.logger.error(`Auto agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async executeAgentTask(task: string, options: any): Promise<void> {
    this.logger.info(`🤖 Starting Agent Mode`);
    this.logger.info(`📋 Task: ${task}`);

    try {
      await this.agentCore.startAgent(task, options);
    } catch (error) {
      this.logger.error(`Agent execution failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private async handleAnalyzeMode(options: any): Promise<void> {
    const { choice } = await inquirer.prompt([
      {
        type: 'list',
        name: 'choice',
        message: 'What would you like me to analyze?',
        choices: [
          'Entire codebase overview',
          'Specific file or directory',
          'Code quality and improvements',
          'Architecture and patterns'
        ]
      }
    ]);

    switch (choice) {
      case 'Entire codebase overview':
        await this.handleAnalyze({});
        break;
      case 'Specific file or directory':
        const { filePath } = await inquirer.prompt([
          {
            type: 'input',
            name: 'filePath',
            message: 'Enter file or directory path:'
          }
        ]);
        if (filePath) {
          await this.handleExplain(filePath, {});
        }
        break;
      default:
        await this.executeAgentTask(`Analyze codebase for ${choice.toLowerCase()}`, options);
    }
  }

  private async handleEditMode(options: any): Promise<void> {
    const { filePath } = await inquirer.prompt([
      {
        type: 'input',
        name: 'filePath',
        message: 'Enter file path to edit:'
      }
    ]);

    if (filePath) {
      const { instruction } = await inquirer.prompt([
        {
          type: 'input',
          name: 'instruction',
          message: 'What changes would you like to make?',
          validate: (input) => input.trim() !== '' || 'Please provide an instruction'
        }
      ]);

      await this.handleEdit(filePath, { instruction });
    }
  }

  private async handleCreateMode(options: any): Promise<void> {
    const { feature } = await inquirer.prompt([
      {
        type: 'input',
        name: 'feature',
        message: 'Describe the feature you want to create:',
        validate: (input) => input.trim() !== '' || 'Please describe the feature'
      }
    ]);

    await this.executeAgentTask(`Create new feature: ${feature}`, options);
  }



  private async showCurrentConfig(): Promise<void> {
    const config = this.config.getConfig();
    
    console.log(boxen(
      `Current Configuration\n\n` +
      `Default Provider: ${config.defaultProvider}\n` +
      `Config Path: ${this.config.getConfigPath()}\n`,
      { padding: 1, borderColor: 'cyan' }
    ));

    const table = new Table({
      head: ['Provider', 'Status', 'Model', 'URL'],
      colWidths: [12, 12, 20, 40]
    });

    for (const [name, provider] of Object.entries(config.providers)) {
      const status = provider.enabled ? 
        (provider.apiKey || name === 'ollama' ? chalk.green('✓ Ready') : chalk.yellow('⚠ No API Key')) :
        chalk.red('✗ Disabled');
      
      table.push([
        name,
        status,
        provider.defaultModel || 'N/A',
        provider.baseUrl || 'N/A'
      ]);
    }

    console.log(table.toString());
  }

  private async configureProviders(): Promise<void> {
    const config = this.config.getConfig();
    const providers = Object.keys(config.providers);

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Which provider would you like to configure?',
        choices: providers
      }
    ]);

    const providerConfig = config.providers[provider as keyof typeof config.providers];
    
    const { apiKey, enabled } = await inquirer.prompt([
      {
        type: 'password',
        name: 'apiKey',
        message: `Enter API key for ${provider}:`,
        when: provider !== 'ollama',
        default: providerConfig.apiKey
      },
      {
        type: 'confirm',
        name: 'enabled',
        message: `Enable ${provider}?`,
        default: providerConfig.enabled
      }
    ]);

    if (apiKey) {
      this.config.setProviderApiKey(provider, apiKey);
    }
    this.config.enableProvider(provider, enabled);

    this.logger.success(`${provider} configuration updated`);
  }

  private async setDefaultProvider(): Promise<void> {
    const availableProviders = await this.aiManager.getAvailableProviders();
    
    if (availableProviders.length === 0) {
      this.logger.error('No providers are available. Please configure at least one provider first.');
      return;
    }

    const { provider } = await inquirer.prompt([
      {
        type: 'list',
        name: 'provider',
        message: 'Select default provider:',
        choices: availableProviders
      }
    ]);

    this.config.updateConfig({ defaultProvider: provider });
    this.logger.success(`Default provider set to ${provider}`);
  }

  private async configureCodebase(): Promise<void> {
    this.logger.info('Codebase configuration not yet implemented');
  }

  private showChatHelp(): void {
    console.log(boxen(
      'Chat Commands:\n\n' +
      'exit - End the chat session\n' +
      'help - Show this help message\n',
      { padding: 1, borderColor: 'yellow', title: 'Help' }
    ));
  }

  private async promptForTask(): Promise<string> {
    const { task } = await inquirer.prompt([
      {
        type: 'input',
        name: 'task',
        message: 'What task would you like the agent to perform?',
        validate: (input) => input.trim() !== '' || 'Please provide a task description'
      }
    ]);
    return task;
  }

  private async promptForGoal(): Promise<string> {
    const { goal } = await inquirer.prompt([
      {
        type: 'input',
        name: 'goal',
        message: 'What is your high-level goal for the auto agent?',
        validate: (input) => input.trim() !== '' || 'Please provide a goal description'
      }
    ]);
    return goal;
  }





  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}
